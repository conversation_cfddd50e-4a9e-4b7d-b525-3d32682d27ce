import { Router } from "express";
import { ApprovalController } from "../controllers/approvalController";
import { validateBody, validateQuery } from "../middleware/validation";
import { authenticate, requireApprovalRole } from "../middleware/auth";
import {
  approvalActionSchema,
  paginationSchema,
  pendingApprovalsFiltersSchema,
} from "../utils/validation";

const router = Router();

// All routes require authentication and approval role
router.use(authenticate);
router.use(requireApprovalRole);

/**
 * @swagger
 * /api/v1/approvals/{transactionId}/process:
 *   post:
 *     summary: Process approval for a transaction
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ApprovalRequest'
 *     responses:
 *       200:
 *         description: Approval processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Approval role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/:transactionId/process",
  validateBody(approvalActionSchema),
  ApprovalController.processApproval
);

/**
 * @swagger
 * /api/v1/approvals/pending:
 *   get:
 *     summary: Get transactions pending approval
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of transactions pending approval
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Approval role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/pending",
  validateQuery(paginationSchema),
  ApprovalController.getTransactionsForApproval
);

/**
 * @swagger
 * /api/v1/approvals/{transactionId}/history:
 *   get:
 *     summary: Get approval history for a transaction
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: Approval history for the transaction
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Approval role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/:transactionId/history", ApprovalController.getApprovalHistory);

/**
 * @swagger
 * /api/v1/approvals/pending-approvals:
 *   get:
 *     summary: Get transactions pending approval at current user's stage (JSON or CSV export)
 *     description: |
 *       Retrieve transactions that are currently at the approval stage corresponding to the logged-in user's role.
 *       Each role can only see transactions at their specific approval stage that they haven't already acted on.
 *
 *       **Response Formats:**
 *       - **JSON Response** (default): Paginated list of transactions with metadata
 *       - **CSV Export** (export=true): Complete dataset as downloadable CSV file
 *
 *       **Key Features:**
 *       - Excludes transactions the user has already processed (approved/rejected/sent back)
 *       - Shows transactions that have been resubmitted after being sent back
 *       - Results are sorted by creation date (newest first)
 *
 *       **Role-Stage Mapping:**
 *       - SUPERVISOR: Transactions at SUPERVISOR stage
 *       - HEAD_CONSUMER_LENDING: Transactions at HEAD_CONSUMER_LENDING stage
 *       - HEAD_RISK_MANAGEMENT: Transactions at HEAD_RISK_MANAGEMENT stage (final approval)
 *       - ACCOUNTANT: Transactions at ACCOUNTANT stage (ready for disbursement)
 *
 *       **Note:** MANAGING_DIRECTOR stage has been removed from the approval workflow.
 *       All transactions now go directly from HEAD_RISK_MANAGEMENT to ACCOUNTANT stage.
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: accountOfficerIds
 *         schema:
 *           type: string
 *         description: Filter by multiple account officer IDs (comma-separated). Takes precedence over accountOfficerId.
 *         example: "60f7b3b3b3b3b3b3b3b3b3b3,60f7b3b3b3b3b3b3b3b3b3b4,60f7b3b3b3b3b3b3b3b3b3b5"
 *       - in: query
 *         name: loanType
 *         schema:
 *           type: string
 *           enum: [CONSUMER_LOAN_PUBLIC, CONSUMER_LOAN_PRIVATE, SME_INDIVIDUAL, SME_CORPORATE]
 *         description: Filter by loan type
 *         example: "CONSUMER_LOAN_PUBLIC"
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering transactions (YYYY-MM-DD format)
 *         example: "2024-01-01"
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering transactions (YYYY-MM-DD format)
 *         example: "2024-12-31"
 *       - in: query
 *         name: export
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Export results as CSV file when set to true. Default is false for JSON response.
 *         example: true
 *     responses:
 *       200:
 *         description: Pending approvals retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Pending approvals retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: Transaction ID
 *                           customerName:
 *                             type: string
 *                             description: Customer full name
 *                           loanType:
 *                             type: string
 *                             enum: [CONSUMER_LOAN_PUBLIC, CONSUMER_LOAN_PRIVATE, SME_INDIVIDUAL, SME_CORPORATE]
 *                           requestedAmount:
 *                             type: number
 *                             description: Requested loan amount
 *                           status:
 *                             type: string
 *                             enum: [SUBMITTED, IN_PROGRESS, APPROVED]
 *                           currentStage:
 *                             type: string
 *                             enum: [SUPERVISOR, HEAD_CONSUMER_LENDING, HEAD_RISK_MANAGEMENT, ACCOUNTANT]
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                           submittedAt:
 *                             type: string
 *                             format: date-time
 *                           createdBy:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                               firstName:
 *                                 type: string
 *                               lastName:
 *                                 type: string
 *                               email:
 *                                 type: string
 *                           documentsCount:
 *                             type: integer
 *                             description: Number of uploaded documents
 *                           canApprove:
 *                             type: boolean
 *                             description: Whether current user can approve this transaction
 *                           stageInfo:
 *                             type: object
 *                             properties:
 *                               stageName:
 *                                 type: string
 *                                 description: Human-readable stage name
 *                               actionLabel:
 *                                 type: string
 *                                 description: Action label for current stage
 *                               isUrgent:
 *                                 type: boolean
 *                                 description: Whether transaction requires urgent attention
 *                     total:
 *                       type: integer
 *                       description: Total number of transactions
 *                     page:
 *                       type: integer
 *                       description: Current page number
 *                     limit:
 *                       type: integer
 *                       description: Items per page
 *                     totalPages:
 *                       type: integer
 *                       description: Total number of pages
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *               description: CSV file with pending approvals data (when export=true)
 *             example: |
 *               Transaction ID,Customer Name,Email,Phone Number,BVN,Loan Type,Requested Amount,Status,Current Stage,Created Date,Submitted Date,Account Officer,Account Officer Email,Organization,IPPIS Number,Employment Date,Address,Loan Purpose,Repayment Mode,Tenor (Months),Monthly Repayment,Total Repayment,Last Updated
 *               TXN-2024-001,John Doe,<EMAIL>,+*************,***********,Consumer Loan (Public),500000,IN_PROGRESS,HEAD_RISK_MANAGEMENT,01/15/2024 10:30:00,01/15/2024 11:00:00,Jane Smith,<EMAIL>,Federal Ministry of Health,IPPIS123456,01/01/2020,"123 Main St, Lagos, Lagos",Personal Development,SALARY_DEDUCTION,12,45833,550000,01/16/2024 09:15:00
 *         headers:
 *           Content-Disposition:
 *             description: Attachment header with generated filename (when export=true)
 *             schema:
 *               type: string
 *               example: 'attachment; filename="pending-approvals-head_risk_management-consumer_loan_public-********-143022.csv"'
 *           Content-Type:
 *             description: Response content type
 *             schema:
 *               type: string
 *               example: "application/json"
 *           Content-Length:
 *             description: File size in bytes (when export=true)
 *             schema:
 *               type: string
 *               example: "2048"
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Approval role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/pending-approvals",
  validateQuery(paginationSchema.merge(pendingApprovalsFiltersSchema)),
  ApprovalController.getPendingApprovals
);

/**
 * @swagger
 * /api/v1/approvals/my-requests/{transactionId}:
 *   get:
 *     summary: Get a specific transaction for My Requests page
 *     description: |
 *       Retrieve a specific transaction that the user can act on or has previously acted on.
 *       This endpoint is designed for the "My Requests" page where users need to see:
 *       - Transactions currently at their approval stage
 *       - Transactions they have previously acted on
 *
 *       The response includes user-specific context such as whether they can take action,
 *       if they've already acted, and what action they took.
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: Transaction retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Transaction retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     transactionId:
 *                       type: string
 *                     status:
 *                       type: string
 *                       enum: [SUBMITTED, IN_PROGRESS, APPROVED, DISBURSED, COMPLETED]
 *                     currentStage:
 *                       type: string
 *                       enum: [SUPERVISOR, HEAD_CONSUMER_LENDING, HEAD_RISK_MANAGEMENT, ACCOUNTANT]
 *                     userContext:
 *                       type: object
 *                       properties:
 *                         canTakeAction:
 *                           type: boolean
 *                           description: Whether the user can take action on this transaction
 *                         hasActed:
 *                           type: boolean
 *                           description: Whether the user has already acted on this transaction
 *                         userAction:
 *                           type: string
 *                           nullable: true
 *                           description: The action the user took (if any)
 *                         userComments:
 *                           type: string
 *                           nullable: true
 *                           description: Comments the user provided (if any)
 *                         actionDate:
 *                           type: string
 *                           format: date-time
 *                           nullable: true
 *                           description: When the user took action (if any)
 *       404:
 *         description: Transaction not found or access denied
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Approval role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/my-requests/:transactionId",
  ApprovalController.getMyRequestTransaction
);

export default router;
