import prisma from "../db/db";
import { TransactionStatus } from "@prisma/client";
import { OperationalError } from "../middleware/errorHandler";

export interface LoanEligibilityResult {
  isEligible: boolean;
  reason?: string;
  activeLoan?: {
    transactionId: string;
    status: TransactionStatus;
    disbursedAt: Date | null;
    loanTenor: number;
    expectedCompletionDate: Date;
    requestedAmount: number;
  };
  eligibilityDate?: Date;
}

export class LoanEligibilityService {
  /**
   * Check if a user is eligible for a new loan based on their BVN
   * @param bvn - Bank Verification Number
   * @returns LoanEligibilityResult
   */
  static async checkLoanEligibilityByBVN(
    bvn: string
  ): Promise<LoanEligibilityResult> {
    // Define active loan statuses
    const activeLoanStatuses = [
      TransactionStatus.APPROVED,
      TransactionStatus.DISBURSED,
      // Note: We don't include COMPLETED or LOAN_REPAID as those loans are finished
    ];

    // Find any active loans for this BVN
    const activeLoan = await prisma.transaction.findFirst({
      where: {
        bvn: bvn,
        status: {
          in: activeLoanStatuses,
        },
      },
      orderBy: {
        disbursedAt: "desc", // Get the most recent active loan
      },
      select: {
        id: true,
        transactionId: true,
        status: true,
        disbursedAt: true,
        loanTenor: true,
        requestedAmount: true,
        createdAt: true,
      },
    });

    // If no active loan found, user is eligible
    if (!activeLoan) {
      return {
        isEligible: true,
      };
    }

    // Calculate expected completion date
    const expectedCompletionDate = this.calculateLoanCompletionDate(
      activeLoan.disbursedAt || activeLoan.createdAt,
      activeLoan.loanTenor || 12 // Default to 12 months if not set
    );

    // Check if the loan tenor has expired
    const currentDate = new Date();
    const isLoanExpired = currentDate >= expectedCompletionDate;

    if (isLoanExpired) {
      // Loan has expired, user is eligible for a new loan
      return {
        isEligible: true,
      };
    }

    // User has an active loan that hasn't expired yet
    return {
      isEligible: false,
      reason: `You have an active loan (${
        activeLoan.transactionId
      }) that expires on ${expectedCompletionDate.toLocaleDateString()}. You can apply for a new loan after this date.`,
      activeLoan: {
        transactionId: activeLoan.transactionId,
        status: activeLoan.status,
        disbursedAt: activeLoan.disbursedAt,
        loanTenor: activeLoan.loanTenor || 12,
        expectedCompletionDate,
        requestedAmount: activeLoan.requestedAmount || 0,
      },
      eligibilityDate: expectedCompletionDate,
    };
  }

  /**
   * Calculate when a loan should be completed based on disbursement date and tenor
   * @param disbursementDate - Date when loan was disbursed (or created if not disbursed)
   * @param tenorInMonths - Loan tenor in months
   * @returns Expected completion date
   */
  private static calculateLoanCompletionDate(
    disbursementDate: Date,
    tenorInMonths: number
  ): Date {
    const completionDate = new Date(disbursementDate);
    completionDate.setMonth(completionDate.getMonth() + tenorInMonths);
    return completionDate;
  }

  /**
   * Validate loan eligibility and throw error if not eligible
   * @param bvn - Bank Verification Number
   * @throws OperationalError if user is not eligible
   */
  static async validateLoanEligibility(bvn: string): Promise<void> {
    const eligibilityResult = await this.checkLoanEligibilityByBVN(bvn);

    if (!eligibilityResult.isEligible) {
      throw new OperationalError(eligibilityResult.reason!, 400);
    }
  }

  /**
   * Get detailed information about a user's loan history by BVN
   * @param bvn - Bank Verification Number
   * @returns Array of loan history
   */
  static async getLoanHistoryByBVN(bvn: string) {
    const loans = await prisma.transaction.findMany({
      where: {
        bvn: bvn,
        status: {
          not: TransactionStatus.DRAFT, // Exclude draft transactions
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      select: {
        id: true,
        transactionId: true,
        status: true,
        requestedAmount: true,
        loanTenor: true,
        disbursedAt: true,
        completedAt: true,
        createdAt: true,
        submittedAt: true,
        rejectedAt: true,
        rejectionReason: true,
      },
    });

    return loans.map((loan) => ({
      ...loan,
      expectedCompletionDate: loan.disbursedAt
        ? this.calculateLoanCompletionDate(
            loan.disbursedAt,
            loan.loanTenor || 12
          )
        : null,
      isActive:
        loan.status === TransactionStatus.APPROVED ||
        loan.status === TransactionStatus.DISBURSED,
    }));
  }

  /**
   * Mark a loan as completed (when paid off early or tenor expires)
   * @param transactionId - Transaction ID
   * @param completionReason - Reason for completion (e.g., "PAID_OFF_EARLY", "TENOR_EXPIRED")
   */
  static async markLoanAsCompleted(
    transactionId: string,
    completionReason?: string
  ): Promise<void> {
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      select: { id: true, status: true, transactionId: true },
    });

    if (!transaction) {
      throw new OperationalError("Transaction not found", 404);
    }

    if (transaction.status === TransactionStatus.COMPLETED) {
      throw new OperationalError("Transaction is already completed", 400);
    }

    if (
      transaction.status !== TransactionStatus.DISBURSED &&
      transaction.status !== TransactionStatus.APPROVED
    ) {
      throw new OperationalError(
        "Only disbursed or approved loans can be marked as completed",
        400
      );
    }

    await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        status: TransactionStatus.COMPLETED,
        completedAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }

  /**
   * Mark a loan as repaid (when user has paid back their loan)
   * @param transactionId - Transaction ID
   * @param repaymentDetails - Optional repayment details
   */
  static async markLoanAsRepaid(
    transactionId: string,
    repaymentDetails?: {
      repaymentMethod?: string;
      repaymentReference?: string;
      notes?: string;
    }
  ): Promise<void> {
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      select: { id: true, status: true, transactionId: true },
    });

    if (!transaction) {
      throw new OperationalError("Transaction not found", 404);
    }

    if (transaction.status === TransactionStatus.LOAN_REPAID) {
      throw new OperationalError("Loan is already marked as repaid", 400);
    }

    if (transaction.status === TransactionStatus.COMPLETED) {
      throw new OperationalError("Loan is already completed", 400);
    }

    if (transaction.status !== TransactionStatus.DISBURSED) {
      throw new OperationalError(
        "Only disbursed loans can be marked as repaid",
        400
      );
    }

    const now = new Date();
    await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        status: TransactionStatus.LOAN_REPAID,
        repaidAt: now,
        completedAt: now, // Mark as completed when repaid
        updatedAt: now,
      },
    });
  }

  /**
   * Handle defaulted loans (special case for eligibility)
   * @param transactionId - Transaction ID
   * @param defaultReason - Reason for default
   */
  static async markLoanAsDefaulted(
    transactionId: string,
    defaultReason: string
  ): Promise<void> {
    // For now, we'll mark defaulted loans as completed to prevent new applications
    // In a real system, you might want a separate DEFAULTED status
    await this.markLoanAsCompleted(
      transactionId,
      `DEFAULTED: ${defaultReason}`
    );
  }

  /**
   * Check if a specific transaction can be submitted (eligibility check during submission)
   * @param transactionId - Transaction ID to check
   * @param bvn - BVN from the transaction
   */
  static async validateTransactionSubmission(
    transactionId: string,
    bvn: string
  ): Promise<void> {
    // Get the current transaction
    const currentTransaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      select: { id: true, status: true, bvn: true },
    });

    if (!currentTransaction) {
      throw new OperationalError("Transaction not found", 404);
    }

    if (
      currentTransaction.status !== TransactionStatus.DRAFT &&
      currentTransaction.status !== TransactionStatus.SENT_BACK
    ) {
      throw new OperationalError(
        "Only draft or sent-back transactions can be submitted",
        400
      );
    }

    // Check eligibility excluding the current transaction
    const activeLoan = await prisma.transaction.findFirst({
      where: {
        bvn: bvn,
        status: {
          in: [TransactionStatus.APPROVED, TransactionStatus.DISBURSED],
        },
        id: {
          not: transactionId, // Exclude current transaction
        },
      },
      select: {
        transactionId: true,
        status: true,
        disbursedAt: true,
        loanTenor: true,
        createdAt: true,
      },
    });

    if (activeLoan) {
      const expectedCompletionDate = this.calculateLoanCompletionDate(
        activeLoan.disbursedAt || activeLoan.createdAt,
        activeLoan.loanTenor || 12
      );

      const currentDate = new Date();
      if (currentDate < expectedCompletionDate) {
        throw new OperationalError(
          `Cannot submit loan application. You have an active loan (${
            activeLoan.transactionId
          }) that expires on ${expectedCompletionDate.toLocaleDateString()}. You can apply for a new loan after this date.`,
          400
        );
      }
    }
  }
}
