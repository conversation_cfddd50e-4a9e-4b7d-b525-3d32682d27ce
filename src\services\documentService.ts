import prisma from "../db/db";
import { FileUploadUtils } from "../utils/fileUpload";
import { TransactionStatus } from "@prisma/client";
import { R2Service } from "./r2Service";
import { OperationalError } from "../middleware/errorHandler";

export class DocumentService {
  static async uploadDocument(
    transactionId: string,
    file: Express.Multer.File,
    userId: string,
    metadata?: string
  ): Promise<{
    id: string;
    fileName: string;
    originalName: string;
    fileUrl: string;
    metadata?: string;
  }> {
    // Verify transaction belongs to user and can be edited
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        createdById: userId,
        status: {
          in: [TransactionStatus.DRAFT, TransactionStatus.SENT_BACK],
        },
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found or cannot be edited",
        404
      );
    }

    // Check if R2 is configured
    if (!R2Service.isR2Configured()) {
      throw new OperationalError(
        "File storage service (R2) is not configured. Please contact administrator.",
        503
      );
    }

    try {
      // Upload file to R2
      const r2Result = await FileUploadUtils.uploadToR2(file, transactionId);

      // Create document record with R2 data
      const document = await prisma.document.create({
        data: {
          transactionId,
          fileName: r2Result.key, // Store R2 key as fileName for backward compatibility
          originalName: file.originalname,
          filePath: r2Result.url, // Store R2 public URL
          r2Key: r2Result.key, // R2 object key
          r2Bucket: r2Result.bucket, // R2 bucket name
          r2Etag: r2Result.etag, // R2 ETag for integrity
          fileSize: r2Result.fileSize,
          fileType: FileUploadUtils.getFileType(file.mimetype),
          mimeType: file.mimetype,
          metadata: metadata || null,
        },
      });

      return {
        id: document.id,
        fileName: document.fileName,
        originalName: document.originalName,
        fileUrl: document.filePath,
        metadata: document.metadata || undefined,
      };
    } catch (error) {
      console.error("Error uploading document to R2:", error);
      throw new OperationalError(
        `Failed to upload document: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        500
      );
    }
  }

  static async uploadMultipleDocuments(
    transactionId: string,
    files: Express.Multer.File[],
    userId: string,
    metadata?: string[]
  ): Promise<{
    successful: Array<{
      id: string;
      fileName: string;
      originalName: string;
      fileUrl: string;
      metadata?: string;
    }>;
    failed: Array<{ originalName: string; error: string }>;
  }> {
    // Verify transaction belongs to user and can be edited
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        createdById: userId,
        status: {
          in: [TransactionStatus.DRAFT, TransactionStatus.SENT_BACK],
        },
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found or cannot be edited",
        404
      );
    }

    // Check if R2 is configured
    if (!R2Service.isR2Configured()) {
      throw new OperationalError(
        "File storage service (R2) is not configured. Please contact administrator.",
        503
      );
    }

    const successful: Array<{
      id: string;
      fileName: string;
      originalName: string;
      fileUrl: string;
      metadata?: string;
    }> = [];
    const failed: Array<{ originalName: string; error: string }> = [];

    // Process each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileMetadata = metadata && metadata[i] ? metadata[i] : undefined;

      try {
        // Upload file to R2
        const r2Result = await FileUploadUtils.uploadToR2(file, transactionId);

        // Create document record with R2 data
        const document = await prisma.document.create({
          data: {
            transactionId,
            fileName: r2Result.key, // Store R2 key as fileName for backward compatibility
            originalName: file.originalname,
            filePath: r2Result.url, // Store R2 public URL
            r2Key: r2Result.key, // R2 object key
            r2Bucket: r2Result.bucket, // R2 bucket name
            r2Etag: r2Result.etag, // R2 ETag for integrity
            fileSize: r2Result.fileSize,
            fileType: FileUploadUtils.getFileType(file.mimetype),
            mimeType: file.mimetype,
            metadata: fileMetadata || null,
          },
        });

        successful.push({
          id: document.id,
          fileName: document.fileName,
          originalName: document.originalName,
          fileUrl: document.filePath,
          metadata: document.metadata || undefined,
        });
      } catch (error) {
        console.error(`Error uploading file ${file.originalname}:`, error);
        failed.push({
          originalName: file.originalname,
          error:
            error instanceof Error ? error.message : "Unknown error occurred",
        });
      }
    }

    return { successful, failed };
  }

  static async getTransactionDocuments(
    transactionId: string,
    userId: string,
    userRole: string
  ): Promise<any[]> {
    // Build where clause based on user role
    const whereClause: any = { id: transactionId };

    // Account officers can only see their own transactions
    if (userRole === "ACCOUNT_OFFICER") {
      whereClause.createdById = userId;
    }
    // FIXED: Removed restrictive access control for other roles to match TransactionService
    // Other roles (supervisors, heads, directors, accountants) can see all transactions
    // This ensures consistency with TransactionService.getTransaction and getTransactionPreview

    const transaction = await prisma.transaction.findFirst({
      where: whereClause,
      include: {
        documents: {
          orderBy: {
            uploadedAt: "desc",
          },
        },
      },
    });

    if (!transaction) {
      throw new OperationalError("Transaction not found", 404);
    }

    return transaction.documents.map((doc) => ({
      id: doc.id,
      fileName: doc.fileName,
      originalName: doc.originalName,
      fileSize: doc.fileSize,
      fileType: doc.fileType,
      mimeType: doc.mimeType,
      uploadedAt: doc.uploadedAt,
      fileUrl: doc.filePath, // Use filePath which contains the R2 public URL
      formattedSize: FileUploadUtils.formatFileSize(doc.fileSize),
      metadata: doc.metadata || undefined,
    }));
  }

  static async deleteDocument(
    documentId: string,
    userId: string
  ): Promise<void> {
    // Find document and verify permissions
    const document = await prisma.document.findFirst({
      where: {
        id: documentId,
      },
      include: {
        transaction: {
          select: {
            createdById: true,
            status: true,
          },
        },
      },
    });

    if (!document) {
      throw new OperationalError("Document not found", 404);
    }

    // Check if user owns the transaction
    if (document.transaction.createdById !== userId) {
      throw new OperationalError(
        "You do not have permission to delete this document",
        403
      );
    }

    // Check if transaction can be edited
    // Check if transaction status allows document deletion (only DRAFT or SENT_BACK statuses)
    if (
      !(["DRAFT", "SENT_BACK"] as TransactionStatus[]).includes(
        document.transaction.status
      )
    ) {
      throw new OperationalError(
        "Cannot delete document from transaction in current status",
        400
      );
    }

    // Delete file from Cloudinary using cloudinaryId if available, fallback to fileName
    const publicIdToDelete = document.cloudinaryId || document.fileName;
    await FileUploadUtils.deleteFile(publicIdToDelete);

    // Delete document record
    await prisma.document.delete({
      where: { id: documentId },
    });
  }

  static async getDocumentFile(
    documentId: string,
    userId: string,
    userRole: string
  ): Promise<{ fileUrl: string; fileName: string; mimeType: string }> {
    // Build where clause for document lookup
    const document = await prisma.document.findFirst({
      where: {
        id: documentId,
      },
      include: {
        transaction: {
          select: {
            createdById: true,
          },
        },
      },
    });

    if (!document) {
      throw new OperationalError("Document not found", 404);
    }

    // Check permissions - account officers can only access their own documents
    if (
      userRole === "ACCOUNT_OFFICER" &&
      document.transaction.createdById !== userId
    ) {
      throw new OperationalError(
        "You do not have permission to access this document",
        403
      );
    }

    return {
      fileUrl: document.filePath, // This is now the R2 public URL
      fileName: document.originalName,
      mimeType: document.mimeType,
    };
  }

  static async getDocumentStats(
    transactionId: string,
    userId: string,
    userRole: string
  ): Promise<{
    totalDocuments: number;
    totalSize: number;
    formattedTotalSize: string;
    documentTypes: Array<{ type: string; count: number }>;
    uploadedToday: number;
    lastUploadDate: Date | null;
  }> {
    // Verify transaction exists and user has access
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        ...(userRole === "ACCOUNT_OFFICER" ? { createdById: userId } : {}),
      },
    });

    if (!transaction) {
      throw new OperationalError("Transaction not found or access denied", 404);
    }

    // Get document statistics
    const [documents, documentTypeCounts] = await Promise.all([
      prisma.document.findMany({
        where: { transactionId },
        select: {
          fileSize: true,
          fileType: true,
          uploadedAt: true,
          originalName: true,
        },
      }),
      prisma.document.groupBy({
        by: ["fileType"],
        where: { transactionId },
        _count: {
          fileType: true,
        },
      }),
    ]);

    // Calculate statistics
    const totalDocuments = documents.length;
    const totalSize = documents.reduce(
      (sum, doc) => sum + (doc.fileSize || 0),
      0
    );
    const formattedTotalSize = this.formatFileSize(totalSize);

    // Count documents uploaded today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const uploadedToday = documents.filter(
      (doc) => new Date(doc.uploadedAt) >= today
    ).length;

    // Get last upload date
    const lastUploadDate =
      documents.length > 0
        ? new Date(
            Math.max(
              ...documents.map((doc) => new Date(doc.uploadedAt).getTime())
            )
          )
        : null;

    // Format document types
    const documentTypes = documentTypeCounts.map((item) => ({
      type: item.fileType || "Unknown",
      count: item._count.fileType,
    }));

    return {
      totalDocuments,
      totalSize,
      formattedTotalSize,
      documentTypes,
      uploadedToday,
      lastUploadDate,
    };
  }

  private static formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  static async generateDocumentSignedUrl(
    documentId: string,
    userId: string,
    userRole: string,
    expiresInSeconds: number = 3600
  ): Promise<{
    signedUrl: string;
    expiresAt: string;
    documentId: string;
    fileName: string;
  }> {
    // Find document and verify permissions using same logic as getDocumentFile
    const document = await prisma.document.findFirst({
      where: {
        id: documentId,
      },
      include: {
        transaction: {
          select: {
            createdById: true,
          },
        },
      },
    });

    if (!document) {
      throw new OperationalError("Document not found", 404);
    }

    // Check permissions - account officers can only access their own documents
    if (
      userRole === "ACCOUNT_OFFICER" &&
      document.transaction.createdById !== userId
    ) {
      throw new OperationalError(
        "You do not have permission to access this document",
        403
      );
    }

    // Log access attempt for audit purposes
    console.log(
      `📋 Signed URL requested for document ${documentId} by user ${userId} (${userRole})`
    );

    let signedUrl: string;

    try {
      // Check if document uses R2 storage (has r2Key) or Cloudinary (has cloudinaryId)
      if (document.r2Key) {
        // Use R2Service for signed URL generation
        const { R2Service } = await import("./r2Service");
        signedUrl = await R2Service.generateSignedUrl(
          document.r2Key,
          expiresInSeconds
        );
      } else if (document.cloudinaryId) {
        // Use CloudinaryService for signed URL generation
        const { CloudinaryService } = await import("./cloudinaryService");
        signedUrl = await CloudinaryService.getDownloadableUrl(
          document.cloudinaryId
        );
      } else {
        // Fallback to direct file path (for backward compatibility)
        signedUrl = document.filePath;
      }
    } catch (error) {
      console.error(
        `❌ Failed to generate signed URL for document ${documentId}:`,
        error
      );
      throw new OperationalError(
        "Failed to generate signed URL for document",
        500
      );
    }

    // Calculate expiration timestamp
    const expiresAt = new Date(
      Date.now() + expiresInSeconds * 1000
    ).toISOString();

    return {
      signedUrl,
      expiresAt,
      documentId,
      fileName: document.originalName,
    };
  }
}
