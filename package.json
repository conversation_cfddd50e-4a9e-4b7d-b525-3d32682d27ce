{"name": "ams-loan-system", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "tsnd --respawn -r tsconfig-paths/register --pretty --transpile-only ./src/index.ts", "build": "tsc && tsc-alias", "start": "node ./dist", "postinstall": "prisma generate", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "tsnd -r tsconfig-paths/register ./src/scripts/seed.ts", "db:studio": "prisma studio", "cleanup": "tsnd -r tsconfig-paths/register ./scripts/manual-cleanup.ts --", "cleanup:stats": "tsnd -r tsconfig-paths/register ./scripts/manual-cleanup.ts -- --stats", "cleanup:otp": "tsnd -r tsconfig-paths/register ./scripts/manual-cleanup.ts -- --otp", "cleanup:notifications": "tsnd -r tsconfig-paths/register ./scripts/manual-cleanup.ts -- --notifications", "cleanup:password-reset": "tsnd -r tsconfig-paths/register ./scripts/manual-cleanup.ts -- --password-reset", "cleanup:all": "tsnd -r tsconfig-paths/register ./scripts/manual-cleanup.ts -- --all", "lint": "tsc --noEmit", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/archiver": "^6.0.3", "@types/cors": "^2.8.19", "@types/morgan": "^1.9.10", "@types/node": "^22.5.1", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.4"}, "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@prisma/client": "^5.19.0", "@types/bcryptjs": "^2.4.6", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.13", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.14.0", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "archiver": "^7.0.1", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cloudinary": "^2.6.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cron": "^4.2.1", "nodemailer": "^7.0.3", "pdfkit": "^0.17.1", "prisma": "^5.19.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.25.64"}}