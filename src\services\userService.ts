import prisma from "../db/db";
import { UserRole, TransactionStatus } from "@prisma/client";
import { OperationalError } from "../middleware/errorHandler";

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  email?: string;
  role?: UserRole;
  monthlyTarget?: number;
  isActive?: boolean;
}

export class UserService {
  /**
   * Delete a user with safety checks
   */
  static async deleteUser(userId: string, adminId: string): Promise<void> {
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        createdTransactions: {
          where: {
            status: {
              in: [
                TransactionStatus.DRAFT,
                TransactionStatus.SUBMITTED,
                TransactionStatus.IN_PROGRESS,
                TransactionStatus.APPROVED,
                TransactionStatus.DISBURSED,
              ],
            },
          },
        },
        approvals: {
          where: {
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
        },
      },
    });

    if (!user) {
      throw new OperationalError("User not found", 404);
    }

    // Prevent deletion of users with active transactions
    if (user.createdTransactions.length > 0) {
      throw new OperationalError(
        `Cannot delete user with ${user.createdTransactions.length} active transactions. Please complete or reassign these transactions first.`,
        400
      );
    }

    // Prevent deletion of users with recent approvals
    if (user.approvals.length > 0) {
      throw new OperationalError(
        `Cannot delete user with recent approval activities. Please wait for pending approvals to be resolved.`,
        400
      );
    }

    // Prevent deletion of the last Super Admin
    if (user.role === UserRole.SUPER_ADMIN) {
      const superAdminCount = await prisma.user.count({
        where: {
          role: UserRole.SUPER_ADMIN,
          isActive: true,
        },
      });

      if (superAdminCount <= 1) {
        throw new OperationalError(
          "Cannot delete the last Super Admin user. Create another Super Admin first.",
          400
        );
      }
    }

    // Prevent self-deletion
    if (userId === adminId) {
      throw new OperationalError(
        "You cannot delete your own account. Ask another Super Admin to perform this action.",
        400
      );
    }

    // Perform the deletion
    await prisma.user.delete({
      where: { id: userId },
    });

    console.log("User deletion completed:", {
      deletedUserId: userId,
      deletedUserEmail: user.email,
      deletedUserRole: user.role,
      deletedBy: adminId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Update user details with validation
   */
  static async updateUser(
    userId: string,
    updateData: UpdateUserData,
    updatedBy: string
  ): Promise<any> {
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      throw new OperationalError("User not found", 404);
    }

    // Validate email uniqueness if email is being updated
    if (updateData.email && updateData.email !== existingUser.email) {
      const emailExists = await prisma.user.findFirst({
        where: {
          email: updateData.email,
          id: { not: userId },
        },
      });

      if (emailExists) {
        throw new OperationalError(
          "Email already exists for another user",
          400
        );
      }
    }

    // Validate role if being updated
    if (updateData.role && !Object.values(UserRole).includes(updateData.role)) {
      throw new OperationalError("Invalid role specified", 400);
    }

    // Prevent deactivating the last Super Admin
    if (
      updateData.isActive === false &&
      existingUser.role === UserRole.SUPER_ADMIN
    ) {
      const activeSuperAdminCount = await prisma.user.count({
        where: {
          role: UserRole.SUPER_ADMIN,
          isActive: true,
          id: { not: userId },
        },
      });

      if (activeSuperAdminCount === 0) {
        throw new OperationalError(
          "Cannot deactivate the last active Super Admin",
          400
        );
      }
    }

    // Prepare update data
    const updatePayload: any = {};
    
    if (updateData.firstName !== undefined) {
      updatePayload.firstName = updateData.firstName;
    }
    if (updateData.lastName !== undefined) {
      updatePayload.lastName = updateData.lastName;
    }
    if (updateData.email !== undefined) {
      updatePayload.email = updateData.email;
    }
    if (updateData.role !== undefined) {
      updatePayload.role = updateData.role;
    }
    if (updateData.monthlyTarget !== undefined) {
      updatePayload.monthlyTarget = updateData.monthlyTarget;
    }
    if (updateData.isActive !== undefined) {
      updatePayload.isActive = updateData.isActive;
    }

    // Always update the updatedAt timestamp
    updatePayload.updatedAt = new Date();

    // Perform the update
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updatePayload,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        monthlyTarget: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    console.log("User update completed:", {
      updatedUserId: userId,
      updatedFields: Object.keys(updatePayload),
      updatedBy,
      timestamp: new Date().toISOString(),
    });

    return updatedUser;
  }

  /**
   * Get user by ID with safety checks
   */
  static async getUserById(userId: string): Promise<any> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        monthlyTarget: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new OperationalError("User not found", 404);
    }

    return user;
  }

  /**
   * Check if user has active transactions or pending approvals
   */
  static async checkUserDependencies(userId: string): Promise<{
    hasActiveTransactions: boolean;
    hasPendingApprovals: boolean;
    activeTransactionCount: number;
    pendingApprovalCount: number;
  }> {
    const [activeTransactions, pendingApprovals] = await Promise.all([
      prisma.transaction.count({
        where: {
          createdById: userId,
          status: {
            in: [
              TransactionStatus.DRAFT,
              TransactionStatus.SUBMITTED,
              TransactionStatus.IN_PROGRESS,
              TransactionStatus.APPROVED,
              TransactionStatus.DISBURSED,
            ],
          },
        },
      }),
      prisma.transactionApproval.count({
        where: {
          approverId: userId,
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    ]);

    return {
      hasActiveTransactions: activeTransactions > 0,
      hasPendingApprovals: pendingApprovals > 0,
      activeTransactionCount: activeTransactions,
      pendingApprovalCount: pendingApprovals,
    };
  }
}
