import { Response } from "express";
import { ApprovalService } from "../services/approvalService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { asyncHandler } from "../middleware/errorHandler";

export class ApprovalController {
  static processApproval = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const { action, comments } = req.body;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      await ApprovalService.processApproval(transactionId, userId, userRole, {
        action,
        comments,
      });

      ResponseHandler.success(
        res,
        `Transaction ${action.toLowerCase()} successfully`
      );
    }
  );

  static getTransactionsForApproval = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userRole = req.user!.role;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const result = await ApprovalService.getTransactionsForApproval(
        userRole,
        page,
        limit
      );

      ResponseHandler.success(
        res,
        "Transactions for approval retrieved successfully",
        result
      );
    }
  );

  static getApprovalHistory = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;

      const history = await ApprovalService.getApprovalHistory(transactionId);

      ResponseHandler.success(
        res,
        "Approval history retrieved successfully",
        history
      );
    }
  );

  static getPendingApprovals = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userRole = req.user!.role;
      const userId = req.user!.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const accountOfficerId = req.query.accountOfficerId as string;
      const loanType = req.query.loanType as string;
      const dateFrom = req.query.dateFrom as string;
      const dateTo = req.query.dateTo as string;
      const exportCsv = (req.query.export as string) === "true";

      // Handle multiple account officer IDs
      let accountOfficerIds: string[] | undefined;
      if (req.query.accountOfficerIds) {
        if (typeof req.query.accountOfficerIds === "string") {
          accountOfficerIds = (req.query.accountOfficerIds as string)
            .split(",")
            .map((id) => id.trim())
            .filter((id) => id.length > 0);
        } else if (Array.isArray(req.query.accountOfficerIds)) {
          accountOfficerIds = req.query.accountOfficerIds as string[];
        }
      }

      const filters = {
        page,
        limit,
        accountOfficerId,
        accountOfficerIds,
        loanType,
        dateFrom,
        dateTo,
      };

      // Handle CSV export
      if (exportCsv) {
        console.log("CSV export request:", {
          userRole,
          userId,
          filters,
        });

        // Generate CSV content
        const csvContent = await ApprovalService.exportPendingApprovalsCSV(
          userRole,
          userId,
          filters
        );

        // Validate CSV content
        if (!csvContent || typeof csvContent !== "string") {
          console.error("❌ Invalid CSV content:", typeof csvContent);
          return ResponseHandler.error(
            res,
            "Failed to generate CSV content",
            undefined,
            500
          );
        }

        // Check if CSV content looks like JSON (debugging)
        if (csvContent.startsWith("{") || csvContent.startsWith("[")) {
          console.error(
            "❌ CSV content appears to be JSON:",
            csvContent.substring(0, 100)
          );
          return ResponseHandler.error(
            res,
            "CSV generation error - received JSON instead of CSV",
            undefined,
            500
          );
        }

        // Generate filename
        const filename = ApprovalService.generatePendingApprovalsCSVFilename(
          userRole,
          filters
        );

        // Set response headers for CSV download
        res.setHeader("Content-Type", "text/csv; charset=utf-8");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="${filename}"`
        );
        res.setHeader("Content-Length", Buffer.byteLength(csvContent, "utf8"));
        res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        res.setHeader("Pragma", "no-cache");
        res.setHeader("Expires", "0");

        console.log("CSV export successful:", {
          userRole,
          filename,
          contentLength: Buffer.byteLength(csvContent, "utf8"),
          firstLine: csvContent.split("\n")[0],
        });

        return res.send(csvContent);
      }

      // Handle regular JSON response
      const result = await ApprovalService.getPendingApprovals(
        userRole,
        userId,
        filters
      );

      ResponseHandler.success(
        res,
        "Pending approvals retrieved successfully",
        result
      );
    }
  );

  static getMyRequestTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      const transaction = await ApprovalService.getMyRequestTransaction(
        transactionId,
        userId,
        userRole
      );

      ResponseHandler.success(
        res,
        "Transaction retrieved successfully",
        transaction
      );
    }
  );
}
