import prisma from "../db/db";
import { AuthUtils } from "../utils/auth";
import { NotificationService } from "./notificationService";
import { R2Service } from "./r2Service";
import {
  TransactionStatus,
  ApprovalStage,
  UserRole,
  Gender,
  MaritalStatus,
  RepaymentMode,
  LoanType,
} from "@prisma/client";
import { TransactionFilters, PaginationParams } from "../types";
import {
  LoanTypeSelectionData,
  getRequiredFieldsForLoanType,
} from "../types/loanTypes";
// REMOVED: EmailService import - transaction emails disabled
import { OperationalError } from "../middleware/errorHandler";
import { LoanEligibilityService } from "./loanEligibilityService";

export interface PersonalInfoData {
  firstName: string;
  middleName?: string;
  lastName: string;
  gender: Gender;
  maritalStatus: MaritalStatus;
  dateOfBirth: Date;
  bvn: string;
  nin: string;
  email?: string;
  phoneNumber: string;
  street: string;
  city: string;
  state: string;
  postalCode?: string;
  employmentDate: Date;

  // Consumer Loan Public specific
  organizationName?: string;
  ippisNumber?: string;

  // Consumer Loan Private & SME Individual specifics
  remitaActivation?: string;

  // SME Corporate specific
  businessName?: string;
  registrationNumber?: string;
}

export interface NextOfKinData {
  nokFirstName: string;
  nokMiddleName: string;
  nokLastName: string;
  nokRelationship: string;
  nokPhoneNumber: string;
  nokStreet: string;
  nokCity: string;
  nokState: string;
  nokPostalCode?: string;
}

export interface LoanInfoData {
  requestedAmount: number;
  loanTenor: number;
  repaymentMode: RepaymentMode;
  grossPay: number;
  netPay: number;
  purposeOfLoan: string;
}

export interface DisbursementData {
  accountName: string;
  accountNumber: string;
  bankName: string;
}

export class TransactionService {
  static async createTransaction(
    createdById: string
  ): Promise<{ id: string; transactionId: string }> {
    const transactionId = AuthUtils.generateTransactionId();

    const transaction = await prisma.transaction.create({
      data: {
        transactionId,
        createdById,
        status: TransactionStatus.DRAFT,
      },
    });

    return {
      id: transaction.id,
      transactionId: transaction.transactionId,
    };
  }

  static async setLoanType(
    transactionId: string,
    data: LoanTypeSelectionData,
    userId: string
  ): Promise<void> {
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        createdById: userId,
        status: {
          in: [TransactionStatus.DRAFT, TransactionStatus.SENT_BACK],
        },
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found or cannot be edited. Only draft or sent-back transactions can be edited.",
        404
      );
    }

    await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        loanType: data.loanType,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * Validate BVN uniqueness across all transactions
   * @param bvn - Bank Verification Number to validate
   * @param currentTransactionId - Current transaction ID (to exclude from uniqueness check)
   * @throws OperationalError if BVN already exists in another transaction
   */
  private static async validateBVNUniqueness(
    bvn: string,
    currentTransactionId: string
  ): Promise<void> {
    if (!bvn) return; // Skip validation if BVN is not provided

    // Normalize BVN (remove spaces, convert to uppercase for consistency)
    const normalizedBVN = bvn.toString().trim().replace(/\s+/g, "");

    // Check if BVN already exists in any other transaction
    const existingTransaction = await prisma.transaction.findFirst({
      where: {
        bvn: {
          equals: normalizedBVN,
          mode: "insensitive", // Case-insensitive comparison
        },
        id: {
          not: currentTransactionId, // Exclude current transaction
        },
      },
      select: {
        id: true,
        transactionId: true,
        status: true,
        firstName: true,
        lastName: true,
        createdAt: true,
      },
    });

    if (existingTransaction) {
      console.warn("BVN uniqueness validation failed:", {
        bvn: normalizedBVN,
        currentTransactionId,
        existingTransactionId: existingTransaction.id,
        existingTransactionNumber: existingTransaction.transactionId,
        existingTransactionStatus: existingTransaction.status,
      });

      throw new OperationalError(
        "BVN already exists in the system. Each BVN can only be used for one loan application.",
        400
      );
    }

    console.log("BVN uniqueness validation passed:", {
      bvn: normalizedBVN,
      currentTransactionId,
    });
  }

  static async updatePersonalInfo(
    transactionId: string,
    data: PersonalInfoData,
    userId: string
  ): Promise<void> {
    console.log("UpdatePersonalInfo called with:", {
      transactionId,
      userId,
      dataKeys: Object.keys(data),
      hasBVN: !!data.bvn,
    });

    // Validate ObjectId format
    if (!transactionId || transactionId.length !== 24) {
      console.error("Invalid transaction ID format:", transactionId);
      throw new OperationalError("Invalid transaction ID format", 400);
    }

    // First check if transaction exists at all
    const existingTransaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      select: {
        id: true,
        createdById: true,
        status: true,
        transactionId: true,
      },
    });

    console.log("Existing transaction:", existingTransaction);

    if (!existingTransaction) {
      console.error("Transaction not found in database:", transactionId);
      throw new OperationalError("Transaction not found", 404);
    }

    // Check ownership
    if (existingTransaction.createdById !== userId) {
      console.error("Transaction ownership mismatch:", {
        transactionCreatedBy: existingTransaction.createdById,
        requestingUser: userId,
      });
      throw new OperationalError("Transaction does not belong to user", 403);
    }

    // Check status - allow both DRAFT and SENT_BACK
    if (
      existingTransaction.status !== TransactionStatus.DRAFT &&
      existingTransaction.status !== TransactionStatus.SENT_BACK
    ) {
      console.error("Transaction not in editable status:", {
        currentStatus: existingTransaction.status,
        transactionId: existingTransaction.transactionId,
      });
      throw new OperationalError(
        `Transaction cannot be edited. Current status: ${existingTransaction.status}. Only draft or sent-back transactions can be edited.`,
        400
      );
    }

    // Verify transaction belongs to user and is in editable status
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        createdById: userId,
        status: {
          in: [TransactionStatus.DRAFT, TransactionStatus.SENT_BACK],
        },
      },
    });

    if (!transaction) {
      console.error("Transaction validation failed after checks");
      throw new OperationalError(
        "Transaction not found or cannot be edited. Only draft or sent-back transactions can be edited.",
        404
      );
    }

    // Validate BVN uniqueness if BVN is being updated
    if (data.bvn) {
      await this.validateBVNUniqueness(data.bvn, transactionId);
    }

    // Validate required fields based on loan type if it's set
    if (transaction.loanType) {
      const requiredFields = getRequiredFieldsForLoanType(transaction.loanType);
      const missingFields: string[] = [];

      for (const field of requiredFields) {
        if (!data[field as keyof PersonalInfoData]) {
          missingFields.push(field);
        }
      }

      if (missingFields.length > 0) {
        throw new OperationalError(
          `Missing required fields for ${
            transaction.loanType
          }: ${missingFields.join(", ")}`,
          400
        );
      }
    }

    await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
  }

  static async updateNextOfKin(
    transactionId: string,
    data: NextOfKinData,
    userId: string
  ): Promise<void> {
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        createdById: userId,
        status: {
          in: [TransactionStatus.DRAFT, TransactionStatus.SENT_BACK],
        },
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found or cannot be edited. Only draft or sent-back transactions can be edited.",
        404
      );
    }

    await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
  }

  static async updateLoanInfo(
    transactionId: string,
    data: LoanInfoData,
    userId: string
  ): Promise<void> {
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        createdById: userId,
        status: {
          in: [TransactionStatus.DRAFT, TransactionStatus.SENT_BACK],
        },
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found or cannot be edited. Only draft or sent-back transactions can be edited.",
        404
      );
    }

    await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
  }

  static async updateDisbursement(
    transactionId: string,
    data: DisbursementData,
    userId: string
  ): Promise<void> {
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        createdById: userId,
        status: {
          in: [TransactionStatus.DRAFT, TransactionStatus.SENT_BACK],
        },
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found or cannot be edited. Only draft or sent-back transactions can be edited.",
        404
      );
    }

    await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
  }

  static async submitTransaction(
    transactionId: string,
    userId: string
  ): Promise<void> {
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        createdById: userId,
        status: {
          in: [TransactionStatus.DRAFT, TransactionStatus.SENT_BACK],
        },
      },
      include: {
        documents: true,
        // Remove the invalid personalInfo field
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found or cannot be submitted",
        404
      );
    }

    // Validate that all required fields are filled
    const validationResult = this.validateTransactionCompleteness(transaction);
    if (!validationResult.isComplete) {
      let errorMessage = "Transaction is incomplete.";

      if (validationResult.missingFields.length > 0) {
        errorMessage += ` Missing required fields: ${validationResult.missingFields.join(
          ", "
        )}.`;
      }

      if (validationResult.missingDocuments) {
        errorMessage += " Please upload required documents.";
      }

      throw new OperationalError(errorMessage, 400);
    }

    // Validate loan eligibility based on BVN
    if (transaction.bvn) {
      await LoanEligibilityService.validateTransactionSubmission(
        transactionId,
        transaction.bvn
      );
    } else {
      throw new OperationalError(
        "BVN is required for loan eligibility validation.",
        400
      );
    }

    // Update transaction status
    await prisma.transaction.update({
      where: { id: transactionId },
      data: {
        status: TransactionStatus.SUBMITTED,
        submittedAt: new Date(),
        currentStage: ApprovalStage.SUPERVISOR,
      },
    });

    // Add logging for debugging
    console.log(
      `🚀 Transaction ${transaction.transactionId} submitted/resubmitted:`,
      {
        transactionId: transaction.transactionId,
        previousStatus: transaction.status,
        newStatus: TransactionStatus.SUBMITTED,
        newStage: ApprovalStage.SUPERVISOR,
        submittedBy: userId,
        submittedAt: new Date().toISOString(),
        isResubmission: transaction.status === TransactionStatus.SENT_BACK,
      }
    );

    // Get all supervisors
    const supervisors = await prisma.user.findMany({
      where: {
        role: UserRole.SUPERVISOR,
        isActive: true,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
      },
    });

    // Notify supervisors in-app
    await NotificationService.notifyRoleUsers(
      UserRole.SUPERVISOR,
      "New Transaction Submitted",
      `Transaction ${transaction.transactionId} has been submitted for review.`,
      transactionId
    );

    // REMOVED: Email notifications to supervisors
    // Transaction workflow emails have been disabled - only in-app notifications are sent
    if (supervisors.length > 0) {
      console.log(
        `📧 [DISABLED] Would send email notifications to ${supervisors.length} supervisors for transaction ${transaction.transactionId}`
      );
      // Email sending disabled - only in-app notifications are sent above
    }
  }

  static async getTransaction(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<any> {
    const whereClause: any = { id: transactionId };

    // Account officers can only see their own transactions
    if (userRole === UserRole.ACCOUNT_OFFICER) {
      whereClause.createdById = userId;
    }
    // Remove the else block to allow other roles to see all transactions
    // else {
    //   // Other roles can only see disbursed/completed transactions
    //   whereClause.status = {
    //     in: [TransactionStatus.DISBURSED, TransactionStatus.COMPLETED],
    //   };
    // }

    const transaction = await prisma.transaction.findFirst({
      where: whereClause,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        documents: true,
        approvals: {
          include: {
            approver: {
              select: {
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    if (!transaction) {
      throw new OperationalError("Transaction not found", 404);
    }

    // If R2 is not configured, filter out document URLs to prevent errors
    if (!R2Service.isR2Configured() && transaction.documents) {
      transaction.documents = transaction.documents.map((doc: any) => ({
        ...doc,
        filePath: null, // Remove potentially broken R2 URLs
        fileUrl: null,
        r2Key: doc.r2Key || null,
      }));
    }

    return transaction;
  }

  static async getTransactions(
    filters: TransactionFilters,
    pagination: PaginationParams,
    userId: string,
    userRole: UserRole
  ): Promise<{
    transactions: any[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      page = 1,
      limit = 10,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = pagination;
    const skip = (page - 1) * limit;

    const whereClause: any = {};

    // Apply role-based filtering
    if (userRole === UserRole.ACCOUNT_OFFICER) {
      whereClause.createdById = userId;
    }
    // Remove the else block to allow other roles to see all transactions
    // else {
    //   // For all other roles, only show disbursed/completed transactions
    //   whereClause.status = {
    //     in: [TransactionStatus.DISBURSED, TransactionStatus.COMPLETED],
    //   };
    // }

    // Apply filters
    if (filters.status) {
      whereClause.status = filters.status;
    }
    if (filters.stage) {
      whereClause.currentStage = filters.stage;
    }
    if (filters.createdById) {
      whereClause.createdById = filters.createdById;
    }
    if (filters.dateFrom || filters.dateTo) {
      whereClause.createdAt = {};
      if (filters.dateFrom) {
        whereClause.createdAt.gte = filters.dateFrom;
      }
      if (filters.dateTo) {
        whereClause.createdAt.lte = filters.dateTo;
      }
    }
    if (filters.search) {
      whereClause.OR = [
        { transactionId: { contains: filters.search, mode: "insensitive" } },
        { firstName: { contains: filters.search, mode: "insensitive" } },
        { lastName: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where: whereClause,
        include: {
          createdBy: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      prisma.transaction.count({ where: whereClause }),
    ]);

    return {
      transactions,
      total,
      page,
      limit,
    };
  }

  static async deleteTransaction(
    transactionId: string,
    userId: string
  ): Promise<void> {
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        createdById: userId,
        status: {
          in: [TransactionStatus.DRAFT, TransactionStatus.SENT_BACK],
        },
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found or cannot be deleted.",
        404
      );
    }

    await prisma.transaction.delete({
      where: { id: transactionId },
    });
  }

  private static validateTransactionCompleteness(transaction: any): {
    isComplete: boolean;
    missingFields: string[];
    missingDocuments: boolean;
  } {
    // Base required fields for all loan types
    const baseRequiredFields = [
      "firstName",
      "lastName",
      "gender",
      "maritalStatus",
      "dateOfBirth",
      "bvn",
      "nin",
      "phoneNumber",
      "street",
      "city",
      "state",
      "employmentDate",
      "nokFirstName",
      // "nokMiddleName", // ✅ REMOVED - This is optional
      "nokLastName",
      "nokRelationship",
      "nokPhoneNumber",
      "nokStreet",
      "nokCity",
      "nokState",
      "requestedAmount",
      "loanTenor",
      "repaymentMode",
      "grossPay",
      "netPay",
      "purposeOfLoan",
      "accountName",
      "accountNumber",
      "bankName",
    ];

    // Get loan type specific required fields
    let loanTypeSpecificFields: string[] = [];
    if (transaction.loanType) {
      loanTypeSpecificFields = getRequiredFieldsForLoanType(
        transaction.loanType
      );
    } else {
      // For backward compatibility, default to Consumer Loan Public fields
      loanTypeSpecificFields = ["organizationName", "ippisNumber"];
    }

    const allRequiredFields = [
      ...baseRequiredFields,
      ...loanTypeSpecificFields,
    ];

    const missingFields: string[] = [];

    for (const field of allRequiredFields) {
      if (!transaction[field]) {
        // Convert camelCase to readable format
        const readableField = field
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase())
          .trim();
        missingFields.push(readableField);
      }
    }

    // Check if at least one document is uploaded
    const missingDocuments =
      !transaction.documents || transaction.documents.length === 0;

    if (missingDocuments) {
      missingFields.push("Documents");
    }

    return {
      isComplete: missingFields.length === 0,
      missingFields,
      missingDocuments,
    };
  }

  private static isTransactionComplete(transaction: any): boolean {
    const result = this.validateTransactionCompleteness(transaction);
    return result.isComplete;
  }

  static async getValidationDetails(transaction: any): Promise<{
    isComplete: boolean;
    missingFields: string[];
    missingDocuments: boolean;
  }> {
    return this.validateTransactionCompleteness(transaction);
  }

  static async getTransactionPreview(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<any> {
    const whereClause: any = { id: transactionId };

    // Account officers can only see their own transactions
    if (userRole === UserRole.ACCOUNT_OFFICER) {
      whereClause.createdById = userId;
    }

    const transaction = await prisma.transaction.findFirst({
      where: whereClause,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        documents: {
          select: {
            id: true,
            fileName: true,
            originalName: true,
            filePath: true,
            fileSize: true,
            fileType: true,
            uploadedAt: true,
          },
        },
        approvals: {
          include: {
            approver: {
              select: {
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    if (!transaction) {
      throw new OperationalError("Transaction not found", 404);
    }

    // If R2 is not configured, filter out document URLs to prevent errors
    if (!R2Service.isR2Configured() && transaction.documents) {
      transaction.documents = transaction.documents.map((doc: any) => ({
        ...doc,
        filePath: null, // Remove potentially broken R2 URLs
        fileUrl: null,
        r2Key: doc.r2Key || null,
      }));
    }

    // Format the transaction data for preview
    return {
      ...transaction,
      loanTypeConfig: transaction.loanType
        ? {
            id: transaction.loanType,
            name: this.getLoanTypeName(transaction.loanType),
            requiredFields: getRequiredFieldsForLoanType(transaction.loanType),
          }
        : null,
      isComplete: this.isTransactionComplete(transaction),
      canEdit:
        transaction.status === TransactionStatus.DRAFT &&
        userRole === UserRole.ACCOUNT_OFFICER &&
        transaction.createdById === userId,
    };
  }

  private static getLoanTypeName(loanType: LoanType): string {
    const loanTypeNames = {
      [LoanType.CONSUMER_LOAN_PUBLIC]: "Consumer Loan Public",
      [LoanType.CONSUMER_LOAN_PRIVATE]: "Consumer Loan Private",
      [LoanType.SME_INDIVIDUAL]: "SME Individual",
      [LoanType.SME_CORPORATE]: "SME Corporate",
    };
    return loanTypeNames[loanType] || "Unknown";
  }
}
