import prisma from "../db/db";
import { NotificationService } from "./notificationService";
// REMOVED: EmailService import - transaction emails disabled
import { FileUploadUtils } from "../utils/fileUpload";
import { TransactionStatus, ApprovalStage, UserRole } from "@prisma/client";
import { OperationalError } from "../middleware/errorHandler";
import { CSVUtils } from "../utils/csvUtils";

export interface ApprovalAction {
  action:
    | "APPROVED"
    | "REJECTED"
    | "SENT_BACK"
    | "DISBURSED"
    | "SUBMITTED"
    | "LOAN_REPAID";
  comments?: string;
}

export class ApprovalService {
  // Define role to stage mapping
  private static readonly ROLE_TO_STAGE: Record<
    UserRole,
    ApprovalStage | null
  > = {
    [UserRole.SUPER_ADMIN]: null,
    [UserRole.ACCOUNT_OFFICER]: ApprovalStage.ACCOUNT_OFFICER,
    [UserRole.SUPERVISOR]: ApprovalStage.SUPERVISOR,
    [UserRole.HEAD_CONSUMER_LENDING]: ApprovalStage.HEAD_CONSUMER_LENDING,
    [UserRole.HEAD_RISK_MANAGEMENT]: ApprovalStage.HEAD_RISK_MANAGEMENT,
    [UserRole.MANAGING_DIRECTOR]: null, // MANAGING_DIRECTOR removed from approval workflow
    [UserRole.ACCOUNTANT]: ApprovalStage.ACCOUNTANT,
  };

  static async processApproval(
    transactionId: string,
    approverId: string,
    userRole: UserRole,
    action: ApprovalAction
  ): Promise<void> {
    console.log(
      `🔍 Processing approval: ${action.action} by ${userRole} for transaction ${transactionId}`
    );
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        createdBy: true,
        approvals: true,
      },
    });

    if (!transaction) {
      throw new OperationalError("Transaction not found", 404);
    }

    console.log(
      `📋 Transaction state: ${transaction.status}, Current stage: ${transaction.currentStage}`
    );

    // Check if transaction is in a state that can be processed
    const validStatuses: TransactionStatus[] = [
      TransactionStatus.SUBMITTED,
      TransactionStatus.IN_PROGRESS,
    ];

    // For accountants, also allow APPROVED status for disbursement
    if (userRole === UserRole.ACCOUNTANT) {
      validStatuses.push(TransactionStatus.APPROVED);
    }

    if (!validStatuses.includes(transaction.status)) {
      throw new OperationalError(
        "Transaction cannot be processed in its current state",
        400
      );
    }

    // Verify user has permission to approve at current stage
    const requiredStage = this.ROLE_TO_STAGE[userRole];
    if (!requiredStage) {
      throw new OperationalError("Invalid user role for approval", 403);
    }

    // Users can only act at their current stage
    if (transaction.currentStage !== requiredStage) {
      throw new OperationalError(
        "You do not have permission to approve this transaction at its current stage",
        403
      );
    }

    // Validate action based on user role
    const validActionsForRole: Record<UserRole, string[]> = {
      [UserRole.ACCOUNT_OFFICER]: [], // Account officers don't perform approval actions
      [UserRole.SUPERVISOR]: ["SUBMITTED", "SENT_BACK"],
      [UserRole.HEAD_CONSUMER_LENDING]: ["APPROVED", "REJECTED", "SENT_BACK"],
      [UserRole.HEAD_RISK_MANAGEMENT]: ["APPROVED", "REJECTED", "SENT_BACK"],
      [UserRole.MANAGING_DIRECTOR]: [], // MANAGING_DIRECTOR removed from approval workflow
      [UserRole.ACCOUNTANT]: ["DISBURSED", "LOAN_REPAID", "SENT_BACK"],
      [UserRole.SUPER_ADMIN]: [], // Super admins don't perform approval actions directly
    };

    const allowedActions = validActionsForRole[userRole];
    if (!allowedActions.includes(action.action)) {
      const error = new Error(
        `${userRole} cannot perform ${
          action.action
        } action. Allowed actions: ${allowedActions.join(", ")}`
      );
      (error as any).statusCode = 400;
      (error as any).isOperational = true;
      throw error;
    }

    // Check if an approval already exists for this transaction at this stage
    const existingApproval = await prisma.transactionApproval.findUnique({
      where: {
        transactionId_stage: {
          transactionId: transaction.id,
          stage: requiredStage,
        },
      },
    });

    // If approval exists, check if it's by the same user and if transaction has been sent back since
    if (existingApproval) {
      // If it's a different user trying to act at the same stage, block it
      if (existingApproval.approverId !== approverId) {
        throw new OperationalError(
          "Another user has already acted on this transaction at this stage. Only one user can perform actions at each approval stage.",
          409 // Conflict status code
        );
      }

      // If same user, check if transaction was resubmitted after their last action
      // This allows re-action after send-back and resubmission cycles
      const wasResubmittedAfterApproval =
        transaction.submittedAt &&
        new Date(transaction.submittedAt) >
          new Date(existingApproval.createdAt);

      // If transaction wasn't resubmitted after user's action, they can't act again
      if (!wasResubmittedAfterApproval) {
        throw new OperationalError(
          "You have already performed an action on this transaction at this stage. You can only act again if the transaction is sent back and resubmitted.",
          409 // Conflict status code
        );
      }

      console.log(
        `✅ User ${approverId} can act again on transaction ${transaction.transactionId} - resubmitted after their last action:`,
        {
          lastApprovalDate: existingApproval.createdAt,
          resubmittedAt: transaction.submittedAt,
          stage: requiredStage,
        }
      );
    }

    // Process the approval action
    await this.executeApprovalAction(
      transaction,
      approverId,
      requiredStage,
      action
    );
  }

  private static async executeApprovalAction(
    transaction: any,
    approverId: string,
    stage: ApprovalStage,
    action: ApprovalAction
  ): Promise<void> {
    // Create or update approval record (upsert to handle unique constraint)
    await prisma.transactionApproval.upsert({
      where: {
        transactionId_stage: {
          transactionId: transaction.id,
          stage,
        },
      },
      update: {
        approverId,
        action: action.action,
        comments: action.comments,
        createdAt: new Date(), // Update timestamp for new action
      },
      create: {
        transactionId: transaction.id,
        approverId,
        stage,
        action: action.action,
        comments: action.comments,
      },
    });

    let newStatus: TransactionStatus;
    let newStage: ApprovalStage | null = null;
    let notificationTitle: string;
    let notificationMessage: string;

    switch (action.action) {
      case "SUBMITTED":
        // Only supervisors can submit transactions for approval
        if (stage !== ApprovalStage.SUPERVISOR) {
          throw new OperationalError(
            "Only supervisors can submit transactions for approval. This action is restricted to users with supervisor role.",
            403 // Forbidden status code
          );
        }
        newStatus = TransactionStatus.IN_PROGRESS;
        newStage = ApprovalStage.HEAD_CONSUMER_LENDING; // First actual approver
        notificationTitle = "Transaction Submitted for Approval";
        notificationMessage = `Transaction ${transaction.transactionId} has been submitted for approval.`;

        // Notify Head CL (first approver)
        const headCLUsers = await this.getUsersForStage(
          ApprovalStage.HEAD_CONSUMER_LENDING
        );
        for (const user of headCLUsers) {
          await NotificationService.createNotification({
            userId: user.id,
            transactionId: transaction.id,
            type: "TRANSACTION_SUBMITTED",
            title: "Transaction Pending Approval",
            message: `Transaction ${transaction.transactionId} is pending your approval.`,
          });
        }

        // REMOVED: Email notifications to Head CL users
        // Transaction workflow emails have been disabled - only in-app notifications are sent
        if (headCLUsers.length > 0) {
          console.log(
            `📧 [DISABLED] Would send email notifications to ${headCLUsers.length} Head CL users for transaction ${transaction.transactionId}`
          );
          // Email sending disabled - only in-app notifications are sent above
        }
        break;

      case "APPROVED":
        // Only actual approvers can approve (Head CL, Head Risk)
        const approverStages: ApprovalStage[] = [
          ApprovalStage.HEAD_CONSUMER_LENDING,
          ApprovalStage.HEAD_RISK_MANAGEMENT,
        ];
        if (!approverStages.includes(stage)) {
          throw new OperationalError(
            "Only Head Consumer Lending or Head Risk Management can approve transactions. This action is restricted to senior approval roles.",
            403 // Forbidden status code
          );
        }

        // Check if this is the final approval stage
        const nextStage = this.getNextApprovalStage(stage);

        if (nextStage) {
          newStatus = TransactionStatus.IN_PROGRESS;
          newStage = nextStage;
          notificationTitle = "Transaction Approved";
          notificationMessage = `Transaction ${transaction.transactionId} has been approved and moved to the next stage.`;

          // Notify next stage approvers
          const nextRoleUsers = await this.getUsersForStage(nextStage);
          for (const user of nextRoleUsers) {
            await NotificationService.createNotification({
              userId: user.id,
              transactionId: transaction.id,
              type: "TRANSACTION_SUBMITTED",
              title: "Transaction Pending Approval",
              message: `Transaction ${transaction.transactionId} is pending your approval.`,
            });
          }

          // REMOVED: Email notifications to next approval stage users
          // Transaction workflow emails have been disabled - only in-app notifications are sent
          if (nextRoleUsers.length > 0) {
            const roleDisplayName = this.getRoleDisplayName(nextStage);
            console.log(
              `📧 [DISABLED] Would send email notifications to ${nextRoleUsers.length} ${roleDisplayName} users for transaction ${transaction.transactionId}`
            );
            // Email sending disabled - only in-app notifications are sent above
          }
        } else {
          newStatus = TransactionStatus.APPROVED;
          newStage = ApprovalStage.ACCOUNTANT; // Move to accountant for disbursement
          notificationTitle = "Transaction Fully Approved";
          notificationMessage = `Transaction ${transaction.transactionId} has been fully approved and is ready for disbursement.`;

          // Notify accountants
          const accountantUsers = await this.getUsersForStage(
            ApprovalStage.ACCOUNTANT
          );
          for (const user of accountantUsers) {
            await NotificationService.createNotification({
              userId: user.id,
              transactionId: transaction.id,
              type: "TRANSACTION_APPROVED",
              title: "Transaction Ready for Disbursement",
              message: `Transaction ${transaction.transactionId} is ready for disbursement.`,
            });
          }

          // REMOVED: Email notifications to accountants for disbursement
          // Transaction workflow emails have been disabled - only in-app notifications are sent
          if (accountantUsers.length > 0) {
            const roleDisplayName = this.getRoleDisplayName(
              ApprovalStage.ACCOUNTANT
            );
            console.log(
              `📧 [DISABLED] Would send email notifications to ${accountantUsers.length} ${roleDisplayName} users for disbursement of transaction ${transaction.transactionId}`
            );
            // Email sending disabled - only in-app notifications are sent above
          }
        }
        break;

      case "REJECTED":
        newStatus = TransactionStatus.REJECTED;
        notificationTitle = "Transaction Rejected";
        notificationMessage = `Transaction ${
          transaction.transactionId
        } has been rejected. ${action.comments || ""}`;
        break;

      case "SENT_BACK":
        newStatus = TransactionStatus.SENT_BACK;
        newStage = ApprovalStage.ACCOUNT_OFFICER;
        notificationTitle = "Transaction Sent Back";
        notificationMessage = `Transaction ${
          transaction.transactionId
        } has been sent back for corrections. ${action.comments || ""}`;

        // Notify all previous approvers that the transaction was sent back
        await this.notifyPreviousApprovers(
          transaction.id,
          transaction.transactionId
        );
        break;

      case "DISBURSED":
        // Only accountants can disburse
        if (stage !== ApprovalStage.ACCOUNTANT) {
          throw new OperationalError(
            "Only accountants can disburse transactions. This action is restricted to users with accountant role.",
            403 // Forbidden status code
          );
        }
        newStatus = TransactionStatus.DISBURSED;
        notificationTitle = "Transaction Disbursed";
        notificationMessage = `Transaction ${
          transaction.transactionId
        } has been disbursed. ${action.comments || ""}`;
        break;

      case "LOAN_REPAID":
        // Only accountants can mark loans as repaid
        if (stage !== ApprovalStage.ACCOUNTANT) {
          throw new OperationalError(
            "Only accountants can mark loans as repaid. This action is restricted to users with accountant role.",
            403 // Forbidden status code
          );
        }
        // Validate that the transaction is currently disbursed
        if (transaction.status !== TransactionStatus.DISBURSED) {
          throw new OperationalError(
            "Only disbursed loans can be marked as repaid. The loan must be in disbursed status to be marked as repaid.",
            400 // Bad request status code
          );
        }
        newStatus = TransactionStatus.LOAN_REPAID;
        notificationTitle = "Loan Repaid";
        notificationMessage = `Loan ${
          transaction.transactionId
        } has been marked as repaid. ${action.comments || ""}`;
        break;
    }

    // Update transaction
    const updateData: any = {
      status: newStatus,
      updatedAt: new Date(),
    };

    if (newStage !== null) {
      updateData.currentStage = newStage;
    }

    if (action.action === "REJECTED") {
      updateData.rejectedAt = new Date();
      updateData.rejectionReason = action.comments;
    }

    if (action.action === "SENT_BACK") {
      updateData.sentBackReason = action.comments;
    }

    if (action.action === "DISBURSED") {
      updateData.disbursedAt = new Date();
      // Note: completedAt will be set when the loan tenor expires or is paid off
    }

    if (action.action === "LOAN_REPAID") {
      const now = new Date();
      updateData.repaidAt = now;
      updateData.completedAt = now; // Mark as completed when repaid
    }

    await prisma.transaction.update({
      where: { id: transaction.id },
      data: updateData,
    });

    // Send notification to transaction creator
    await NotificationService.createNotification({
      userId: transaction.createdById,
      transactionId: transaction.id,
      type: `TRANSACTION_${action.action}` as any,
      title: notificationTitle,
      message: notificationMessage,
    });

    // REMOVED: Email notification to transaction creator
    // Transaction workflow emails have been disabled - only in-app notifications are sent
    console.log(
      `📧 [DISABLED] Would send email notification to transaction creator: ${transaction.createdBy.email}`
    );
    console.log(
      `📝 [DISABLED] Subject: Transaction ${action.action} - ${transaction.transactionId}`
    );
    console.log(`📝 [DISABLED] Message: ${notificationMessage}`);
    // Email sending disabled - only in-app notifications are sent above
  }

  private static getNextApprovalStage(
    currentStage: ApprovalStage
  ): ApprovalStage | null {
    switch (currentStage) {
      case ApprovalStage.HEAD_CONSUMER_LENDING:
        return ApprovalStage.HEAD_RISK_MANAGEMENT;

      case ApprovalStage.HEAD_RISK_MANAGEMENT:
        // After Head Risk Management approval, go directly to accountant for all amounts
        return null; // This will trigger final approval (skip MANAGING_DIRECTOR)

      default:
        return null;
    }
  }

  private static async getUsersForStage(
    stage: ApprovalStage
  ): Promise<
    { id: string; email: string; firstName: string; lastName: string }[]
  > {
    const roleMap: Record<ApprovalStage, UserRole> = {
      [ApprovalStage.ACCOUNT_OFFICER]: UserRole.ACCOUNT_OFFICER,
      [ApprovalStage.SUPERVISOR]: UserRole.SUPERVISOR,
      [ApprovalStage.HEAD_CONSUMER_LENDING]: UserRole.HEAD_CONSUMER_LENDING,
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: UserRole.HEAD_RISK_MANAGEMENT,
      [ApprovalStage.MANAGING_DIRECTOR]: UserRole.MANAGING_DIRECTOR, // Keep for data integrity
      [ApprovalStage.ACCOUNTANT]: UserRole.ACCOUNTANT,
    };

    return await prisma.user.findMany({
      where: {
        role: roleMap[stage],
        isActive: true,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
      },
    });
  }

  private static getRoleDisplayName(stage: ApprovalStage): string {
    const roleDisplayNames: Record<ApprovalStage, string> = {
      [ApprovalStage.ACCOUNT_OFFICER]: "Account Officer",
      [ApprovalStage.SUPERVISOR]: "Supervisor",
      [ApprovalStage.HEAD_CONSUMER_LENDING]: "Head of Consumer Lending",
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: "Head of Risk Management",
      [ApprovalStage.MANAGING_DIRECTOR]: "Managing Director (Legacy)", // Keep for existing data
      [ApprovalStage.ACCOUNTANT]: "Accountant",
    };

    return roleDisplayNames[stage] || stage;
  }

  private static async notifyPreviousApprovers(
    transactionId: string,
    transactionIdString: string
  ): Promise<void> {
    // Get all users who have approved this transaction
    const approvals = await prisma.transactionApproval.findMany({
      where: { transactionId },
      include: {
        approver: true,
      },
    });

    // Notify each approver
    for (const approval of approvals) {
      await NotificationService.createNotification({
        userId: approval.approverId,
        transactionId,
        type: "TRANSACTION_SENT_BACK",
        title: "Transaction Sent Back",
        message: `Transaction ${transactionIdString} that you approved has been sent back for corrections.`,
      });
    }
  }

  static async getTransactionsForApproval(
    userRole: UserRole,
    page: number = 1,
    limit: number = 10
  ): Promise<{ transactions: any[]; total: number }> {
    const stage = this.ROLE_TO_STAGE[userRole];

    if (!stage) {
      return { transactions: [], total: 0 };
    }

    const skip = (page - 1) * limit;

    const whereClause: any = {
      currentStage: stage,
      status: {
        in: [TransactionStatus.SUBMITTED, TransactionStatus.IN_PROGRESS],
      },
    };

    // For supervisors, only show SUBMITTED transactions (ready for review)
    if (userRole === UserRole.SUPERVISOR) {
      whereClause.status.in = [TransactionStatus.SUBMITTED];
    }

    // For accountants, also include APPROVED transactions ready for disbursement
    if (userRole === UserRole.ACCOUNTANT) {
      whereClause.status.in.push(TransactionStatus.APPROVED);
    }

    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where: whereClause,
        include: {
          createdBy: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          documents: {
            select: {
              id: true,
              fileName: true,
              originalName: true,
              filePath: true,
              fileSize: true,
              fileType: true,
              mimeType: true,
              uploadedAt: true,
            },
            orderBy: {
              uploadedAt: "desc",
            },
          },
        },
        orderBy: {
          submittedAt: "asc",
        },
        skip,
        take: limit,
      }),
      prisma.transaction.count({ where: whereClause }),
    ]);

    // Format the transactions to include enhanced document information
    const formattedTransactions = transactions.map((transaction) => ({
      ...transaction,
      documents: transaction.documents.map((doc) => ({
        id: doc.id,
        fileName: doc.fileName,
        originalName: doc.originalName,
        fileSize: doc.fileSize,
        fileType: doc.fileType,
        mimeType: doc.mimeType,
        uploadedAt: doc.uploadedAt,
        fileUrl: FileUploadUtils.getFileUrl(doc.filePath),
        formattedSize: FileUploadUtils.formatFileSize(doc.fileSize),
      })),
    }));

    return { transactions: formattedTransactions, total };
  }

  static async getApprovalHistory(transactionId: string): Promise<any[]> {
    return await prisma.transactionApproval.findMany({
      where: { transactionId },
      include: {
        approver: {
          select: {
            firstName: true,
            lastName: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });
  }

  static async getDetailedApprovalHistory(transactionId: string): Promise<{
    transactionId: string;
    approvalHistory: Array<{
      stage: string;
      stageName: string;
      action: string;
      approver: {
        id: string;
        name: string;
        role: string;
      };
      comments: string | null;
      timestamp: string;
      formattedDate: string;
    }>;
  }> {
    // First, verify the transaction exists and get its transactionId
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      select: { transactionId: true },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found. The requested transaction does not exist or may have been deleted.",
        404 // Not found status code
      );
    }

    // Get approval history with approver details
    const approvals = await prisma.transactionApproval.findMany({
      where: { transactionId },
      include: {
        approver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Format the approval history
    const approvalHistory = approvals.map((approval) => {
      const stageName = this.getStageDisplayName(approval.stage);
      const formattedDate = new Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "numeric",
        day: "numeric",
      }).format(new Date(approval.createdAt));

      return {
        stage: approval.stage,
        stageName,
        action: approval.action,
        approver: {
          id: approval.approver.id,
          name: `${approval.approver.firstName} ${approval.approver.lastName}`,
          role: approval.approver.role,
        },
        comments: approval.comments,
        timestamp: approval.createdAt.toISOString(),
        formattedDate,
      };
    });

    return {
      transactionId: transaction.transactionId,
      approvalHistory,
    };
  }

  private static getStageDisplayName(stage: ApprovalStage): string {
    const stageNames = {
      [ApprovalStage.ACCOUNT_OFFICER]: "Account Officer",
      [ApprovalStage.SUPERVISOR]: "Supervisor Review",
      [ApprovalStage.HEAD_CONSUMER_LENDING]: "Head Consumer Lending",
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: "Head Risk Management",
      [ApprovalStage.MANAGING_DIRECTOR]: "Managing Director (Legacy)", // Keep for existing data
      [ApprovalStage.ACCOUNTANT]: "Accountant (Disbursement)",
    };

    return stageNames[stage] || stage;
  }

  static async getPendingApprovals(
    userRole: UserRole,
    userId: string,
    filters: {
      accountOfficerId?: string;
      accountOfficerIds?: string[];
      loanType?: string;
      dateFrom?: string;
      dateTo?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    transactions: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const stage = this.ROLE_TO_STAGE[userRole];

    if (!stage) {
      return {
        transactions: [],
        total: 0,
        page: filters.page || 1,
        limit: filters.limit || 10,
        totalPages: 0,
      };
    }

    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const skip = (page - 1) * limit;

    const whereClause: any = {
      currentStage: stage,
      status: {
        in: [TransactionStatus.SUBMITTED, TransactionStatus.IN_PROGRESS],
      },
    };

    // For supervisors, only show SUBMITTED transactions
    if (userRole === UserRole.SUPERVISOR) {
      whereClause.status.in = [TransactionStatus.SUBMITTED];
    }

    // For accountants, also include APPROVED transactions
    if (userRole === UserRole.ACCOUNTANT) {
      whereClause.status.in.push(TransactionStatus.APPROVED);
    }

    // Apply filters
    if (filters.accountOfficerId) {
      whereClause.createdById = filters.accountOfficerId;
    }

    // Handle multiple account officer IDs (takes precedence over single accountOfficerId)
    if (filters.accountOfficerIds && filters.accountOfficerIds.length > 0) {
      whereClause.createdById = {
        in: filters.accountOfficerIds,
      };
    }

    if (filters.loanType) {
      whereClause.loanType = filters.loanType;
    }

    // Apply date range filters
    if (filters.dateFrom || filters.dateTo) {
      whereClause.createdAt = {};

      if (filters.dateFrom) {
        whereClause.createdAt.gte = new Date(filters.dateFrom);
      }

      if (filters.dateTo) {
        // Add 23:59:59 to include the entire day
        const endDate = new Date(filters.dateTo);
        endDate.setHours(23, 59, 59, 999);
        whereClause.createdAt.lte = endDate;
      }
    }

    // Add logging for debugging
    console.log(
      `🔍 Getting pending approvals for ${userRole} (${userId}) at stage ${stage}:`,
      {
        whereClause: JSON.stringify(whereClause, null, 2),
        filters,
      }
    );

    // Get all transactions at this stage with the required status
    const allTransactions = await prisma.transaction.findMany({
      where: whereClause,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        documents: {
          select: {
            id: true,
            originalName: true,
            fileType: true,
          },
        },
        approvals: {
          include: {
            approver: {
              select: {
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Filter transactions: exclude those where user has already acted (unless resubmitted after send-back)
    const eligibleTransactions = allTransactions.filter((transaction) => {
      // Check if user has any approvals at this stage
      const userApprovalsAtStage = transaction.approvals.filter(
        (approval) => approval.approverId === userId && approval.stage === stage
      );

      if (userApprovalsAtStage.length === 0) {
        // User has never acted on this transaction at this stage - include it
        return true;
      }

      // User has acted at this stage - check if transaction was resubmitted after send-back
      // Get the user's most recent approval at this stage
      const lastUserApproval = userApprovalsAtStage.reduce((latest, current) =>
        new Date(current.createdAt) > new Date(latest.createdAt)
          ? current
          : latest
      );

      // Check if the transaction was resubmitted after the user's last action
      // This happens when submittedAt is more recent than the user's last approval
      const wasResubmittedAfterUserAction =
        transaction.submittedAt &&
        new Date(transaction.submittedAt) >
          new Date(lastUserApproval.createdAt);

      console.log(`🔍 Transaction ${transaction.transactionId} filtering:`, {
        userId,
        stage,
        transactionStatus: transaction.status,
        transactionCurrentStage: transaction.currentStage,
        submittedAt: transaction.submittedAt,
        userActionsCount: userApprovalsAtStage.length,
        lastUserApprovalDate: lastUserApproval.createdAt,
        lastUserApprovalAction: lastUserApproval.action,
        wasResubmittedAfterUserAction,
        willInclude: wasResubmittedAfterUserAction,
      });

      // Include if transaction was resubmitted after user's last action
      return wasResubmittedAfterUserAction;
    });

    // Apply pagination to filtered results
    const paginatedTransactions = eligibleTransactions.slice(
      skip,
      skip + limit
    );

    const formattedTransactions = paginatedTransactions.map((transaction) => ({
      id: transaction.id,
      customerName: `${transaction.firstName} ${transaction.lastName}`,
      loanType: transaction.loanType,
      requestedAmount: transaction.requestedAmount,
      status: transaction.status,
      currentStage: transaction.currentStage,
      createdAt: transaction.createdAt,
      submittedAt: transaction.submittedAt,
      createdBy: transaction.createdBy,
      documentsCount: transaction.documents.length,
      approvals: transaction.approvals,
      canApprove: true,
      stageInfo: this.getStageInfo(stage, transaction.status),
    }));

    const total = eligibleTransactions.length;
    const totalPages = Math.ceil(total / limit);

    console.log(`📊 Pending approvals result for ${userRole} (${userId}):`, {
      totalTransactionsAtStage: allTransactions.length,
      eligibleAfterFiltering: total,
      page,
      limit,
      totalPages,
      returnedCount: formattedTransactions.length,
    });

    return {
      transactions: formattedTransactions,
      total,
      page,
      limit,
      totalPages,
    };
  }

  private static getStageInfo(
    stage: ApprovalStage,
    status: TransactionStatus
  ): any {
    const stageNames = {
      [ApprovalStage.ACCOUNT_OFFICER]: "Account Officer",
      [ApprovalStage.SUPERVISOR]: "Supervisor Review",
      [ApprovalStage.HEAD_CONSUMER_LENDING]: "Head Consumer Lending",
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: "Head Risk Management",
      [ApprovalStage.MANAGING_DIRECTOR]: "Managing Director (Legacy)", // Keep for existing data
      [ApprovalStage.ACCOUNTANT]: "Accountant (Disbursement)",
    };

    const actionLabels = {
      [ApprovalStage.ACCOUNT_OFFICER]: "Create/Edit",
      [ApprovalStage.SUPERVISOR]:
        status === TransactionStatus.SUBMITTED ? "Review & Submit" : "Process",
      [ApprovalStage.HEAD_CONSUMER_LENDING]: "Approve/Reject/Send Back",
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: "Approve/Reject/Send Back",
      [ApprovalStage.MANAGING_DIRECTOR]: "Legacy Stage (No Actions)", // No actions available
      [ApprovalStage.ACCOUNTANT]: "Disburse/Send Back",
    };

    return {
      stageName: stageNames[stage] || stage,
      actionLabel: actionLabels[stage] || "Process",
      isUrgent: status === TransactionStatus.SUBMITTED,
    };
  }

  /**
   * Get a specific transaction for the "My Requests" page
   * Users can see transactions that are at their approval stage or that they've previously acted on
   */
  static async getMyRequestTransaction(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<any> {
    const userStage = this.ROLE_TO_STAGE[userRole];

    if (!userStage) {
      throw new OperationalError(
        "Invalid user role for approval requests. Your role does not have permission to access approval requests.",
        403 // Forbidden status code
      );
    }

    // Build where clause for transactions the user can access
    const whereClause: any = { id: transactionId };

    // Super admin can see all transactions
    if (userRole === UserRole.SUPER_ADMIN) {
      // No additional restrictions
    } else {
      // Other roles can see:
      // 1. Transactions currently at their stage
      // 2. Transactions they have previously acted on
      whereClause.OR = [
        // Transactions at their current stage
        {
          currentStage: userStage,
          status: {
            in: [
              TransactionStatus.SUBMITTED,
              TransactionStatus.IN_PROGRESS,
              ...(userRole === UserRole.ACCOUNTANT
                ? [TransactionStatus.APPROVED]
                : []),
            ],
          },
        },
        // Transactions they have previously acted on
        {
          approvals: {
            some: {
              approverId: userId,
              stage: userStage,
            },
          },
        },
      ];
    }

    const transaction = await prisma.transaction.findFirst({
      where: whereClause,
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        documents: true,
        approvals: {
          include: {
            approver: {
              select: {
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    if (!transaction) {
      throw new OperationalError(
        "Transaction not found or access denied. You may not have permission to view this transaction or it may not exist.",
        404 // Not found status code
      );
    }

    // Add user-specific context
    const userApproval = transaction.approvals.find(
      (approval: any) =>
        approval.approverId === userId && approval.stage === userStage
    );

    const validStatusesForAction = [
      TransactionStatus.SUBMITTED,
      TransactionStatus.IN_PROGRESS,
      TransactionStatus.APPROVED,
    ];

    const canTakeAction =
      transaction.currentStage === userStage &&
      !userApproval &&
      validStatusesForAction.includes(transaction.status as any);

    return {
      ...transaction,
      userContext: {
        canTakeAction,
        hasActed: !!userApproval,
        userAction: userApproval?.action || null,
        userComments: userApproval?.comments || null,
        actionDate: userApproval?.createdAt || null,
      },
    };
  }

  /**
   * Export pending approvals as CSV
   */
  static async exportPendingApprovalsCSV(
    userRole: UserRole,
    userId: string,
    filters: {
      accountOfficerId?: string;
      accountOfficerIds?: string[];
      loanType?: string;
      dateFrom?: string;
      dateTo?: string;
    } = {}
  ): Promise<string> {
    console.log("🔄 Exporting pending approvals CSV:", {
      userRole,
      userId,
      filters,
    });

    // Get all pending approvals without pagination
    const result = await this.getPendingApprovals(userRole, userId, {
      ...filters,
      limit: 10000, // Large limit to get all records
      page: 1,
    });

    const transactions = result.transactions;

    console.log(`📊 Exporting ${transactions.length} pending approvals to CSV`);

    if (transactions.length === 0) {
      console.log("⚠️ No transactions found for CSV export");
      return "Transaction ID,Customer Name,Email,Phone Number,BVN,Loan Type,Requested Amount,Status,Current Stage,Created Date\n";
    }

    // Convert to CSV format
    const csvData = transactions.map((transaction) => {
      // Log first transaction for debugging
      if (transactions.indexOf(transaction) === 0) {
        console.log("🔍 First transaction structure:", {
          id: transaction.id,
          transactionId: transaction.transactionId,
          firstName: transaction.firstName,
          status: transaction.status,
          hasLoanTypeConfig: !!transaction.loanTypeConfig,
          hasCreatedBy: !!transaction.createdBy,
        });
      }

      return {
        "Transaction ID": transaction.transactionId || "",
        "Customer Name": `${transaction.firstName || ""} ${
          transaction.middleName || ""
        } ${transaction.lastName || ""}`.trim(),
        Email: transaction.email || "",
        "Phone Number": transaction.phoneNumber || "",
        BVN: transaction.bvn || "",
        "Loan Type":
          transaction.loanTypeConfig?.name || transaction.loanType || "",
        "Requested Amount": transaction.requestedAmount || 0,
        Status: transaction.status || "",
        "Current Stage": transaction.currentStage || "",
        "Created Date": transaction.createdAt
          ? new Date(transaction.createdAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            })
          : "",
        "Submitted Date": transaction.submittedAt
          ? new Date(transaction.submittedAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            })
          : "",
        "Account Officer": transaction.createdBy
          ? `${transaction.createdBy.firstName} ${transaction.createdBy.lastName}`
          : "",
        "Account Officer Email": transaction.createdBy?.email || "",
        Organization: transaction.organizationName || "",
        "IPPIS Number": transaction.ippisNumber || "",
        "Employment Date": transaction.employmentDate
          ? new Date(transaction.employmentDate).toLocaleDateString("en-US")
          : "",
        Address: [
          transaction.street,
          transaction.city,
          transaction.state,
          transaction.postalCode,
        ]
          .filter(Boolean)
          .join(", "),
        "Loan Purpose": transaction.loanPurpose || "",
        "Repayment Mode": transaction.repaymentMode || "",
        "Tenor (Months)": transaction.tenor || "",
        "Monthly Repayment": transaction.monthlyRepayment || 0,
        "Total Repayment": transaction.totalRepayment || 0,
        "Last Updated": transaction.updatedAt
          ? new Date(transaction.updatedAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            })
          : "",
      };
    });

    console.log(`✅ CSV data prepared for ${csvData.length} transactions`);

    // Use the robust CSV utility
    const csvContent = CSVUtils.arrayToCSV(csvData);

    // Validate the generated CSV
    const validation = CSVUtils.validateCSVContent(csvContent);
    if (!validation.isValid) {
      console.error("❌ CSV validation failed:", validation.error);
      throw new OperationalError(`CSV generation failed: ${validation.error}`);
    }

    console.log(`📄 CSV content generated successfully:`, {
      length: csvContent.length,
      lines: validation.lineCount,
      columns: validation.columnCount,
    });

    return csvContent;
  }

  /**
   * Generate filename for pending approvals CSV export
   */
  static generatePendingApprovalsCSVFilename(
    userRole: UserRole,
    filters: any = {}
  ): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");
    const timestamp = `${year}${month}${day}-${hours}${minutes}${seconds}`;

    // Create descriptive filename based on filters
    let filename = `pending-approvals-${userRole.toLowerCase()}`;

    if (filters.loanType) {
      filename += `-${filters.loanType.toLowerCase()}`;
    }

    if (filters.dateFrom && filters.dateTo) {
      const fromDate = new Date(filters.dateFrom).toISOString().split("T")[0];
      const toDate = new Date(filters.dateTo).toISOString().split("T")[0];
      filename += `-${fromDate}-to-${toDate}`;
    } else if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom).toISOString().split("T")[0];
      filename += `-from-${fromDate}`;
    } else if (filters.dateTo) {
      const toDate = new Date(filters.dateTo).toISOString().split("T")[0];
      filename += `-until-${toDate}`;
    }

    filename += `-${timestamp}.csv`;

    return filename;
  }
}
